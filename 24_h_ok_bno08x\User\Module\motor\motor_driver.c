/**
 ******************************************************************************
 * @file    motor_driver.c
 * @brief   DRV8871 电机驱动库源文件
 * <AUTHOR>
 * @date    2025-06-20
 ******************************************************************************
 * @attention
 *
 * 本库专为DRV8871电机驱动芯片设计
 * 支持双电机独立控制，实现DRV8871的多种控制模式
 * 支持浮点精度控制，1000级PWM精度
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "motor_driver.h"
#include <math.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
static int8_t Motor_ValidateParams(Motor_t *motor);
static int16_t Float_To_Speed1000(float speed);
static uint32_t Speed1000_To_PWM(int16_t speed_1000, MotorDecayMode_t decay_mode);
static int8_t Motor_ValidateFloatSpeed(float speed);
static void DRV8871_Control(Motor_t* motor, int16_t speed_1000, MotorDecayMode_t decay_mode);
static void Set_Pin_Mode(GPIO_TypeDef* port, uint16_t pin, PinMode_t target_mode, PinMode_t* current_mode, uint32_t af_config);

/* Exported functions --------------------------------------------------------*/


/**
 * @brief 创建电机实体 (DRV8871双PWM通道)
 */
int8_t Motor_Create(Motor_t *motor,
                    TIM_HandleTypeDef *htim,
                    uint32_t ain1_channel,
                    uint32_t ain2_channel,
                    GPIO_TypeDef *ain1_port,
                    uint16_t ain1_pin,
                    uint32_t ain1_af,
                    GPIO_TypeDef *ain2_port,
                    uint16_t ain2_pin,
                    uint32_t ain2_af,
                    uint8_t reverse)
{
    // 参数检查
    if (motor == NULL || htim == NULL || ain1_port == NULL || ain2_port == NULL)
    {
        return -1;
    }

    // 检查AIN1通道有效性
    if (ain1_channel != TIM_CHANNEL_1 && ain1_channel != TIM_CHANNEL_2 &&
        ain1_channel != TIM_CHANNEL_3 && ain1_channel != TIM_CHANNEL_4)
    {
        return -1;
    }

    // 检查AIN2通道有效性
    if (ain2_channel != TIM_CHANNEL_1 && ain2_channel != TIM_CHANNEL_2 &&
        ain2_channel != TIM_CHANNEL_3 && ain2_channel != TIM_CHANNEL_4)
    {
        return -1;
    }

    // 初始化硬件配置
    motor->hw.htim = htim;
    motor->hw.ain1_channel = ain1_channel;
    motor->hw.ain2_channel = ain2_channel;
    motor->hw.ain1_port = ain1_port;
    motor->hw.ain1_pin = ain1_pin;
    motor->hw.ain1_af = ain1_af;
    motor->hw.ain2_port = ain2_port;
    motor->hw.ain2_pin = ain2_pin;
    motor->hw.ain2_af = ain2_af;
    motor->hw.ain1_mode = PIN_MODE_PWM;  // 初始状态为PWM模式
    motor->hw.ain2_mode = PIN_MODE_PWM;  // 初始状态为PWM模式

    // 初始化电机状态
    motor->speed = 0.0f;
    motor->state = MOTOR_STATE_STOP;
    motor->enable = 1;
    motor->reverse = reverse;
    motor->decay_mode = MOTOR_DECAY_SLOW;  // 默认慢衰减模式，适合低速精确控制

    // 启动双PWM通道
    HAL_TIM_PWM_Start(motor->hw.htim, motor->hw.ain1_channel);
    HAL_TIM_PWM_Start(motor->hw.htim, motor->hw.ain2_channel);

    // 设置初始制动状态：使用DRV8871控制逻辑
    DRV8871_Control(motor, 0, motor->decay_mode);

    return 0;
}

/**
 * @brief 设置电机速度
 */
int8_t Motor_SetSpeed(Motor_t *motor, float speed)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    // 浮点速度范围检查
    if (Motor_ValidateFloatSpeed(speed) != 0)
    {
        return -1;
    }

    // 检查电机是否使能
    if (!motor->enable)
    {
        return -1;
    }

    // 保存速度值
    motor->speed = speed;

    // 转换为1000级精度
    int16_t speed_1000 = Float_To_Speed1000(speed);

    // 处理反装电机：反装电机需要将速度取反
    if (motor->reverse)
    {
        speed_1000 = -speed_1000;
    }

    // 调用DRV8871控制逻辑
    DRV8871_Control(motor, speed_1000, motor->decay_mode);

    // 更新电机状态（简洁的状态管理）
    if (speed == 0.0f)
    {
        motor->state = MOTOR_STATE_STOP;
    }
    else if (speed > 0.0f)
    {
        motor->state = motor->reverse ? MOTOR_STATE_BACKWARD : MOTOR_STATE_FORWARD;
    }
    else
    {
        motor->state = motor->reverse ? MOTOR_STATE_FORWARD : MOTOR_STATE_BACKWARD;
    }

    return 0;
}

/**
 * @brief 停止电机
 */
int8_t Motor_Stop(Motor_t *motor)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    // 使用DRV8871控制逻辑实现制动
    DRV8871_Control(motor, 0, motor->decay_mode);

    // 更新状态
    motor->speed = 0.0f;
    motor->state = MOTOR_STATE_STOP;

    return 0;
}

/**
 * @brief 获取电机状态
 */
MotorState_t Motor_GetState(Motor_t *motor)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return MOTOR_STATE_ERROR;
    }

    return motor->state;
}

/**
 * @brief 使能/失能电机
 */
int8_t Motor_Enable(Motor_t *motor, uint8_t enable)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    motor->enable = enable;

    // 如果失能，立即停止电机
    if (!enable)
    {
        // 使用DRV8871控制逻辑实现制动
        DRV8871_Control(motor, 0, motor->decay_mode);
        motor->speed = 0.0f;
        motor->state = MOTOR_STATE_STOP;
    }

    return 0;
}

/* Private functions ---------------------------------------------------------*/
/**
 * @brief 验证电机参数有效性
 * @param motor: 电机实体指针
 * @retval 0: 有效, -1: 无效
 */
static int8_t Motor_ValidateParams(Motor_t *motor)
{
    if (motor == NULL ||
        motor->hw.htim == NULL ||
        motor->hw.ain1_port == NULL ||
        motor->hw.ain2_port == NULL)
    {
        return -1;
    }

    return 0;
}

/**
 * @brief 将浮点速度转换为1000级精度整数
 * @param speed: 浮点速度值 (-100.0 到 +100.0)
 * @retval 1000级精度整数 (-1000 到 +1000)
 */
static int16_t Float_To_Speed1000(float speed)
{
    return (int16_t)roundf(speed * MOTOR_PRECISION_SCALE);
}

/**
 * @brief 将1000级精度速度转换为PWM比较值，支持不同衰减模式
 * @param speed_1000: 1000级精度速度值 (-1000 到 +1000)
 * @param decay_mode: 衰减模式 (MOTOR_DECAY_SLOW/MOTOR_DECAY_FAST)
 * @retval PWM比较值 (0 到 MOTOR_PWM_PERIOD)
 * @note DRV8871快衰减模式技术原理：
 *       - 慢衰减：AIN1=PWM, AIN2=0，电机电压=VCC*PWM，正比关系
 *       - 快衰减：AIN1=PWM, AIN2=1，电机电压=VCC*(PWM-1)，反比关系
 *       因此快衰减模式需要PWM反转：实际PWM = MOTOR_PWM_PERIOD - 设置PWM
 *
 * @example 快衰减模式PWM计算示例（含最小阈值处理）：
 *          设置1%速度 -> speed_1000=10 -> 原始PWM=10 -> 阈值提升=50 -> 反转PWM=949 -> 实际5%转速
 *          设置10%速度 -> speed_1000=100 -> 原始PWM=99 -> 阈值保持=99 -> 反转PWM=900 -> 实际10%转速
 *          设置90%速度 -> speed_1000=900 -> 原始PWM=899 -> 阈值保持=899 -> 反转PWM=100 -> 实际90%转速
 *
 * @warning 注意事项：
 *          1. 零速度(speed_1000=0)在所有模式下都返回0，确保制动功能正常
 *          2. 处理顺序关键：先应用最小阈值，后执行PWM反转（快衰减模式）
 *          3. 边界检查确保PWM值始终在[0, MOTOR_PWM_PERIOD]范围内
 *          4. 最小PWM阈值：快衰减5%(50/999)，慢衰减60%(599/999)
 *          5. 低于阈值的PWM值会被提升到最小阈值，确保电机能够启动
 */
static uint32_t Speed1000_To_PWM(int16_t speed_1000, MotorDecayMode_t decay_mode)
{
    uint16_t abs_speed = (speed_1000 < 0) ? -speed_1000 : speed_1000;
    if (abs_speed == 0) return 0; // 零速度直接返回0，适用于所有模式

    // 使用宏定义常量进行映射：1000级精度映射到PWM周期
    uint32_t pwm_value = abs_speed * MOTOR_PWM_PERIOD / MOTOR_MAX_PRECISION;
    pwm_value = (pwm_value > MOTOR_PWM_PERIOD) ? MOTOR_PWM_PERIOD : pwm_value; // 边界检查

    // 先应用最小PWM阈值（在PWM反转之前）
    uint32_t min_threshold = (decay_mode == MOTOR_DECAY_FAST) ?
                            MOTOR_MIN_PWM_FAST_DECAY : MOTOR_MIN_PWM_SLOW_DECAY;

    if (pwm_value > 0 && pwm_value < min_threshold) {
        pwm_value = min_threshold; // 低于阈值时设为最小阈值
    }

    // DRV8871快衰减模式PWM反转处理（在阈值处理之后）
    if (decay_mode == MOTOR_DECAY_FAST) {
        pwm_value = MOTOR_PWM_PERIOD - pwm_value; // PWM反转：解决快衰减模式占空比反比问题
    }

    return pwm_value;
}

/**
 * @brief 验证浮点速度参数有效性
 * @param speed: 浮点速度值
 * @retval 0: 有效, -1: 无效
 */
static int8_t Motor_ValidateFloatSpeed(float speed)
{
    return (speed >= MOTOR_SPEED_MIN_FLOAT && speed <= MOTOR_SPEED_MAX_FLOAT) ? 0 : -1;
}

/**
 * @brief DRV8871电机控制逻辑 (状态驱动优化版本)
 * @param motor: 电机实体指针
 * @param speed_1000: 1000级精度速度值 (-1000 到 +1000)
 * @param decay_mode: 衰减模式 (MOTOR_DECAY_SLOW/MOTOR_DECAY_FAST)
 * @note 优化特点：
 *       1. 状态驱动控制：仅在引脚状态变化时执行切换
 *       2. 减少HAL调用：避免频繁的PWM Start/Stop操作
 *       3. 解决GPIO冲突：动态切换PWM复用模式与GPIO模式
 *       4. 支持5种DRV8871控制模式：
 *          - 制动模式：AIN1=0, AIN2=0
 *          - 正转慢衰减：AIN1=PWM, AIN2=0
 *          - 正转快衰减：AIN1=PWM, AIN2=1
 *          - 反转慢衰减：AIN1=0, AIN2=PWM
 *          - 反转快衰减：AIN1=1, AIN2=PWM
 */
static void DRV8871_Control(Motor_t* motor, int16_t speed_1000, MotorDecayMode_t decay_mode)
{
    PinMode_t target_ain1, target_ain2;
    uint32_t pwm_val = 0;

    // 计算目标引脚状态
    if (speed_1000 == 0) {
        // 制动模式：AIN1=0, AIN2=0
        target_ain1 = PIN_MODE_GPIO_LOW;
        target_ain2 = PIN_MODE_GPIO_LOW;
    } else if (speed_1000 > 0) {
        pwm_val = Speed1000_To_PWM(speed_1000, decay_mode);
        if (decay_mode == MOTOR_DECAY_SLOW) {
            // 正转慢衰减：AIN1=PWM, AIN2=0
            target_ain1 = PIN_MODE_PWM;
            target_ain2 = PIN_MODE_GPIO_LOW;
        } else {
            // 正转快衰减：AIN1=PWM, AIN2=1
            target_ain1 = PIN_MODE_PWM;
            target_ain2 = PIN_MODE_GPIO_HIGH;
        }
    } else {
        pwm_val = Speed1000_To_PWM(-speed_1000, decay_mode);
        if (decay_mode == MOTOR_DECAY_SLOW) {
            // 反转慢衰减：AIN1=0, AIN2=PWM
            target_ain1 = PIN_MODE_GPIO_LOW;
            target_ain2 = PIN_MODE_PWM;
        } else {
            // 反转快衰减：AIN1=1, AIN2=PWM
            target_ain1 = PIN_MODE_GPIO_HIGH;
            target_ain2 = PIN_MODE_PWM;
        }
    }

    // 状态驱动的引脚控制 (仅在状态变化时执行切换)
    Set_Pin_Mode(motor->hw.ain1_port, motor->hw.ain1_pin,
                 target_ain1, &motor->hw.ain1_mode, motor->hw.ain1_af);
    Set_Pin_Mode(motor->hw.ain2_port, motor->hw.ain2_pin,
                 target_ain2, &motor->hw.ain2_mode, motor->hw.ain2_af);

    // 设置PWM比较值 (仅对PWM模式的引脚)
    if (target_ain1 == PIN_MODE_PWM) {
        __HAL_TIM_SET_COMPARE(motor->hw.htim, motor->hw.ain1_channel, pwm_val);
    }
    if (target_ain2 == PIN_MODE_PWM) {
        __HAL_TIM_SET_COMPARE(motor->hw.htim, motor->hw.ain2_channel, pwm_val);
    }
}

/**
 * @brief 设置电机衰减模式
 */
int8_t Motor_SetDecayMode(Motor_t* motor, MotorDecayMode_t mode)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    // 检查衰减模式有效性
    if (mode != MOTOR_DECAY_SLOW && mode != MOTOR_DECAY_FAST)
    {
        return -1;
    }

    // 设置衰减模式
    motor->decay_mode = mode;

    // 如果电机正在运行，重新应用当前速度以更新控制模式
    if (motor->speed != 0.0f)
    {
        return Motor_SetSpeed(motor, motor->speed);
    }

    return 0;
}

/**
 * @brief 设置引脚模式 (PWM复用模式 <-> GPIO输出模式)
 * @param port: GPIO端口
 * @param pin: GPIO引脚
 * @param target_mode: 目标引脚模式
 * @param current_mode: 当前引脚模式指针 (用于状态跟踪)
 * @param af_config: 复用功能配置 (如GPIO_AF1_TIM1)
 * @note 仅在状态变化时执行HAL库调用，实现高效的状态驱动控制
 */
static void Set_Pin_Mode(GPIO_TypeDef* port, uint16_t pin,
                         PinMode_t target_mode, PinMode_t* current_mode,
                         uint32_t af_config)
{
    // 状态未变化，直接返回 (核心优化点)
    if (target_mode == *current_mode) {
        return;
    }

    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = pin;
    GPIO_InitStruct.Pull = GPIO_NOPULL;

    if (target_mode == PIN_MODE_PWM) {
        // 切换为PWM复用模式
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
        GPIO_InitStruct.Alternate = af_config;
    } else {
        // 切换为GPIO输出模式
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    }

    // 重新配置GPIO
    HAL_GPIO_Init(port, &GPIO_InitStruct);

    // 如果是GPIO模式，设置输出电平
    if (target_mode != PIN_MODE_PWM) {
        HAL_GPIO_WritePin(port, pin,
            (target_mode == PIN_MODE_GPIO_HIGH) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    }

    // 更新状态跟踪
    *current_mode = target_mode;
}

